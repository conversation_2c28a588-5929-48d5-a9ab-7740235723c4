/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --accent-primary: #3498db;
    --accent-secondary: #e74c3c;
    --border-color: #ecf0f1;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Dark Theme */
    --dark-bg-primary: #1a1a1a;
    --dark-bg-secondary: #2d2d2d;
    --dark-text-primary: #ffffff;
    --dark-text-secondary: #b0b0b0;
    --dark-accent-primary: #64b5f6;
    --dark-accent-secondary: #ff6b6b;
    --dark-border-color: #404040;
    --dark-shadow: rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Cairo', 'Roboto', sans-serif;
    line-height: 1.6;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    overflow-x: hidden;
}

body.dark-mode {
    background: var(--dark-bg-primary);
    color: var(--dark-text-primary);
}

/* Loading Screen */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeOut 1s ease-in-out 2s forwards;
}

.loading-content {
    text-align: center;
    color: white;
}

.code-animation {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 2rem;
}

.code-animation span {
    display: inline-block;
    animation: bounce 1s infinite;
}

.code-animation span:nth-child(1) { animation-delay: 0s; }
.code-animation span:nth-child(2) { animation-delay: 0.2s; }
.code-animation span:nth-child(3) { animation-delay: 0.4s; }

.loading-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.loading-progress {
    width: 0;
    height: 100%;
    background: white;
    border-radius: 2px;
    animation: loadProgress 2s ease-in-out forwards;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.dark-mode .navbar {
    background: rgba(26, 26, 26, 0.95);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.nav-brand i {
    color: var(--accent-primary);
    animation: pulse 2s infinite;
}

.dark-mode .nav-brand i {
    color: var(--dark-accent-primary);
}

.nav-controls {
    display: flex;
    gap: 1rem;
}

.control-btn {
    background: none;
    border: 2px solid var(--accent-primary);
    color: var(--accent-primary);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
}

.dark-mode .control-btn {
    border-color: var(--dark-accent-primary);
    color: var(--dark-accent-primary);
}

.control-btn:hover {
    background: var(--accent-primary);
    color: white;
    transform: translateY(-2px);
}

.dark-mode .control-btn:hover {
    background: var(--dark-accent-primary);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 6rem 2rem 2rem;
}

/* Hero Section */
.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.profile-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 4rem;
    color: white;
    position: relative;
    z-index: 2;
    animation: float 3s ease-in-out infinite;
}

.dark-mode .image-placeholder {
    background: linear-gradient(135deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.tech-orbit {
    position: absolute;
    width: 350px;
    height: 350px;
    border: 2px dashed var(--accent-primary);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.dark-mode .tech-orbit {
    border-color: var(--dark-accent-primary);
}

.orbit-item {
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: white;
    animation: counterRotate 20s linear infinite;
}

.orbit-item.python {
    background: #3776ab;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
}

.orbit-item.php {
    background: #777bb4;
    right: -25px;
    top: 50%;
    transform: translateY(-50%);
}

.orbit-item.windows {
    background: #0078d4;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
}

.orbit-item.linux {
    background: #fcc624;
    left: -25px;
    top: 50%;
    transform: translateY(-50%);
}

.hero-text {
    text-align: right;
}

[dir="ltr"] .hero-text {
    text-align: left;
}

.name-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dark-mode .name-title {
    background: linear-gradient(135deg, var(--dark-accent-primary), var(--dark-accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.typing-text {
    display: inline-block;
    border-right: 3px solid var(--accent-primary);
    animation: typing 3s steps(30) 1s forwards, blink 1s infinite;
    white-space: nowrap;
    overflow: hidden;
    width: 0;
}

.dark-mode .typing-text {
    border-color: var(--dark-accent-primary);
}

.subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    opacity: 0;
    animation: slideUp 1s ease-out 2s forwards;
}

.dark-mode .subtitle {
    color: var(--dark-text-secondary);
}

.hero-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    opacity: 0;
    animation: slideUp 1s ease-out 2.5s forwards;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
}

.info-item i {
    color: var(--accent-primary);
    width: 20px;
}

.dark-mode .info-item i {
    color: var(--dark-accent-primary);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.float-item {
    position: absolute;
    font-size: 2rem;
    color: var(--accent-primary);
    opacity: 0.3;
    animation: float 4s ease-in-out infinite;
}

.dark-mode .float-item {
    color: var(--dark-accent-primary);
}

.float-item:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.float-item:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.float-item:nth-child(3) {
    top: 30%;
    right: 25%;
    animation-delay: 2s;
}

.float-item:nth-child(4) {
    bottom: 30%;
    left: 20%;
    animation-delay: 3s;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    color: var(--text-primary);
}

.dark-mode .section-title {
    color: var(--dark-text-primary);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 2px;
}

.dark-mode .section-title::after {
    background: linear-gradient(135deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

/* Animations */
@keyframes fadeOut {
    to {
        opacity: 0;
        visibility: hidden;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

@keyframes loadProgress {
    to {
        width: 100%;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes counterRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-360deg);
    }
}

@keyframes typing {
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--accent-primary);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fillLevel {
    from {
        width: 0;
    }
    to {
        width: var(--target-width, 100%);
    }
}

/* Skills Section */
.skills-section {
    padding: 4rem 0;
    background: var(--bg-secondary);
    margin: 2rem -2rem;
    border-radius: 20px;
}

.dark-mode .skills-section {
    background: var(--dark-bg-secondary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 0 2rem;
}

.skill-card {
    background: var(--bg-primary);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px var(--shadow);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.dark-mode .skill-card {
    background: var(--dark-bg-primary);
    box-shadow: 0 10px 30px var(--dark-shadow);
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    transition: all 0.3s ease;
}

.dark-mode .skill-card::before {
    background: linear-gradient(90deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.skill-card:hover::before {
    left: 0;
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow);
}

.dark-mode .skill-card:hover {
    box-shadow: 0 20px 40px var(--dark-shadow);
}

.skill-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.dark-mode .skill-icon {
    background: linear-gradient(135deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.skill-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.dark-mode .skill-card h3 {
    color: var(--dark-text-primary);
}

.skill-details {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.skill-card:hover .skill-details {
    max-height: 200px;
}

.skill-details ul {
    list-style: none;
    padding: 0;
}

.skill-details li {
    padding: 0.5rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 1.5rem;
    opacity: 0;
    transform: translateX(-20px);
    animation: slideInLeft 0.3s ease forwards;
}

.dark-mode .skill-details li {
    color: var(--dark-text-secondary);
}

.skill-card:hover .skill-details li {
    animation-delay: calc(var(--i) * 0.1s);
}

.skill-details li:nth-child(1) { --i: 1; }
.skill-details li:nth-child(2) { --i: 2; }
.skill-details li:nth-child(3) { --i: 3; }
.skill-details li:nth-child(4) { --i: 4; }

.skill-details li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-size: 0.8rem;
}

.dark-mode .skill-details li::before {
    color: var(--dark-accent-primary);
}

/* Programming Section */
.programming-section {
    padding: 4rem 0;
}

.programming-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.programming-card {
    background: var(--bg-primary);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 15px 35px var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dark-mode .programming-card {
    background: var(--dark-bg-primary);
    box-shadow: 0 15px 35px var(--dark-shadow);
}

.programming-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #3776ab, #ffd43b);
}

.programming-card.php-card::before {
    background: linear-gradient(90deg, #777bb4, #8892bf);
}

.programming-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.programming-header i {
    font-size: 2rem;
    color: #3776ab;
}

.programming-card.php-card .programming-header i {
    color: #777bb4;
}

.programming-header h3 {
    font-size: 1.8rem;
    color: var(--text-primary);
}

.dark-mode .programming-header h3 {
    color: var(--dark-text-primary);
}

.skill-level {
    margin-left: auto;
    text-align: right;
}

[dir="ltr"] .skill-level {
    margin-right: auto;
    margin-left: 0;
    text-align: left;
}

.skill-level span {
    font-size: 0.9rem;
    color: var(--text-secondary);
    display: block;
    margin-bottom: 0.5rem;
}

.dark-mode .skill-level span {
    color: var(--dark-text-secondary);
}

.level-bar {
    width: 100px;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.dark-mode .level-bar {
    background: var(--dark-border-color);
}

.level-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 4px;
    transition: width 2s ease;
    animation: fillLevel 2s ease forwards;
}

.dark-mode .level-fill {
    background: linear-gradient(90deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.programming-details ul {
    list-style: none;
    padding: 0;
}

.programming-details li {
    padding: 0.7rem 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.dark-mode .programming-details li {
    color: var(--dark-text-secondary);
    border-color: var(--dark-border-color);
}

.programming-details li:last-child {
    border-bottom: none;
}

.programming-details li::before {
    content: '🐍';
    position: absolute;
    left: 0;
    font-size: 1rem;
}

.programming-card.php-card .programming-details li::before {
    content: '🐘';
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-text {
        text-align: center;
    }

    .name-title {
        font-size: 2rem;
    }

    .image-placeholder {
        width: 200px;
        height: 200px;
        font-size: 3rem;
    }

    .tech-orbit {
        width: 280px;
        height: 280px;
    }

    .container {
        padding: 6rem 1rem 2rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }

    .programming-grid {
        grid-template-columns: 1fr;
    }
}

/* Experience Section */
.experience-section {
    padding: 4rem 0;
    background: var(--bg-secondary);
    margin: 2rem -2rem;
    border-radius: 20px;
}

.dark-mode .experience-section {
    background: var(--dark-bg-secondary);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--accent-primary), var(--accent-secondary));
    transform: translateX(-50%);
}

.dark-mode .timeline::before {
    background: linear-gradient(180deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row-reverse;
}

.timeline-marker {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--accent-primary);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    animation: pulse 2s infinite;
}

.dark-mode .timeline-marker {
    background: var(--dark-accent-primary);
}

.timeline-content {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
    width: 45%;
    position: relative;
    transition: all 0.3s ease;
}

.dark-mode .timeline-content {
    background: var(--dark-bg-primary);
    box-shadow: 0 10px 30px var(--dark-shadow);
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px var(--shadow);
}

.dark-mode .timeline-content:hover {
    box-shadow: 0 15px 40px var(--dark-shadow);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 15px solid transparent;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -30px;
    border-left-color: var(--bg-primary);
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -30px;
    border-right-color: var(--bg-primary);
}

.dark-mode .timeline-item:nth-child(odd) .timeline-content::before {
    border-left-color: var(--dark-bg-primary);
}

.dark-mode .timeline-item:nth-child(even) .timeline-content::before {
    border-right-color: var(--dark-bg-primary);
}

.timeline-content h3 {
    font-size: 1.3rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.dark-mode .timeline-content h3 {
    color: var(--dark-text-primary);
}

.duration {
    display: inline-block;
    background: var(--accent-primary);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.dark-mode .duration {
    background: var(--dark-accent-primary);
}

.timeline-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.dark-mode .timeline-content p {
    color: var(--dark-text-secondary);
}

/* Contact Section */
.contact-section {
    padding: 4rem 0;
    text-align: center;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-item {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px var(--shadow);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.dark-mode .contact-item {
    background: var(--dark-bg-primary);
    box-shadow: 0 10px 30px var(--dark-shadow);
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    opacity: 0.1;
    transition: all 0.3s ease;
}

.dark-mode .contact-item::before {
    background: linear-gradient(135deg, var(--dark-accent-primary), var(--dark-accent-secondary));
}

.contact-item:hover::before {
    left: 0;
}

.contact-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px var(--shadow);
}

.dark-mode .contact-item:hover {
    box-shadow: 0 20px 40px var(--dark-shadow);
}

.contact-item i {
    font-size: 2.5rem;
    color: var(--accent-primary);
    margin-bottom: 1rem;
    display: block;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.dark-mode .contact-item i {
    color: var(--dark-accent-primary);
}

.contact-item:hover i {
    transform: scale(1.2);
    animation: pulse 1s infinite;
}

.contact-info {
    position: relative;
    z-index: 2;
    text-align: center;
}

.contact-label {
    display: block;
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.dark-mode .contact-label {
    color: var(--dark-text-secondary);
}

.contact-value {
    display: block;
    font-size: 1.1rem;
    color: var(--text-primary);
    font-weight: 600;
    word-break: break-all;
}

.dark-mode .contact-value {
    color: var(--dark-text-primary);
}

/* Special styling for WhatsApp */
.contact-item:nth-child(3) i {
    color: #25D366;
}

.contact-item:nth-child(3):hover {
    border-color: #25D366;
}

.contact-item:nth-child(3)::before {
    background: linear-gradient(135deg, #25D366, #128C7E);
}

/* Special styling for Email */
.contact-item:nth-child(1) i {
    color: #EA4335;
}

.contact-item:nth-child(1):hover {
    border-color: #EA4335;
}

.contact-item:nth-child(1)::before {
    background: linear-gradient(135deg, #EA4335, #FBBC05);
}

/* Special styling for Phone */
.contact-item:nth-child(2) i {
    color: #4285F4;
}

.contact-item:nth-child(2):hover {
    border-color: #4285F4;
}

.contact-item:nth-child(2)::before {
    background: linear-gradient(135deg, #4285F4, #34A853);
}

/* Special styling for Tap Card */
.contact-item:nth-child(4) i {
    color: #FF6B35;
}

.contact-item:nth-child(4):hover {
    border-color: #FF6B35;
}

.contact-item:nth-child(4)::before {
    background: linear-gradient(135deg, #FF6B35, #F7931E);
}

/* Mobile Responsive for Timeline */
@media (max-width: 768px) {
    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        flex-direction: row !important;
    }

    .timeline-marker {
        left: 20px;
        transform: translateX(-50%);
    }

    .timeline-content {
        width: calc(100% - 60px);
        margin-left: 60px;
    }

    .timeline-content::before {
        left: -30px !important;
        right: auto !important;
        border-right-color: var(--bg-primary) !important;
        border-left-color: transparent !important;
    }

    .dark-mode .timeline-content::before {
        border-right-color: var(--dark-bg-primary) !important;
        border-left-color: transparent !important;
    }

    .contact-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Additional Animations */
@keyframes ripple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}

/* Contact hover effects */
.contact-item:hover .contact-value {
    color: var(--accent-primary);
    transform: scale(1.05);
    transition: all 0.3s ease;
}

.dark-mode .contact-item:hover .contact-value {
    color: var(--dark-accent-primary);
}

/* Success message animation */
.success-message {
    animation: slideInRight 0.3s ease, slideOutRight 0.3s ease 2.7s;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Enhanced contact card animations */
.contact-item:active {
    transform: scale(0.95) !important;
}

.contact-item .contact-info {
    transition: all 0.3s ease;
}

.contact-item:hover .contact-info {
    transform: translateY(-2px);
}

/* Responsive contact grid for mobile */
@media (max-width: 480px) {
    .contact-grid {
        grid-template-columns: 1fr !important;
    }

    .contact-item {
        padding: 1.5rem;
    }

    .contact-value {
        font-size: 0.9rem !important;
    }
}
