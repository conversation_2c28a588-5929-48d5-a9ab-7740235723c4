// Language and Theme Management
class CVManager {
    constructor() {
        this.currentLang = 'ar';
        this.isDarkMode = true;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.setupScrollAnimations();
        this.setupSkillCardInteractions();
        this.setupTypingAnimation();
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.addEventListener('click', () => this.toggleTheme());

        // Language toggle
        const langToggle = document.getElementById('lang-toggle');
        langToggle.addEventListener('click', () => this.toggleLanguage());

        // PDF download
        const pdfDownload = document.getElementById('pdf-download');
        pdfDownload.addEventListener('click', () => this.downloadPDF());

        // Skill card interactions
        const skillCards = document.querySelectorAll('.skill-card');
        skillCards.forEach(card => {
            card.addEventListener('mouseenter', () => this.animateSkillDetails(card));
            card.addEventListener('mouseleave', () => this.resetSkillDetails(card));
        });

        // Contact card interactions
        const contactCards = document.querySelectorAll('.contact-item');
        contactCards.forEach(card => {
            card.addEventListener('click', (e) => this.handleContactClick(e, card));
            card.addEventListener('mouseenter', () => this.animateContactCard(card));
        });

        // Smooth scrolling for navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-scroll]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('data-scroll'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }, 2500);
    }

    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        const body = document.body;
        const themeIcon = document.querySelector('#theme-toggle i');

        if (this.isDarkMode) {
            body.classList.add('dark-mode');
            themeIcon.className = 'fas fa-sun';
        } else {
            body.classList.remove('dark-mode');
            themeIcon.className = 'fas fa-moon';
        }

        // Add theme transition effect
        body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            body.style.transition = '';
        }, 300);
    }

    toggleLanguage() {
        this.currentLang = this.currentLang === 'ar' ? 'en' : 'ar';
        const html = document.documentElement;
        const langButton = document.querySelector('#lang-toggle span');

        if (this.currentLang === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            langButton.textContent = 'EN';
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            langButton.textContent = 'AR';
        }

        // Update all translatable elements
        this.updateLanguageContent();
    }

    updateLanguageContent() {
        const elements = document.querySelectorAll('[data-ar][data-en]');
        elements.forEach(element => {
            const text = this.currentLang === 'ar' ? element.getAttribute('data-ar') : element.getAttribute('data-en');
            element.textContent = text;
        });

        // Restart typing animation for name
        this.setupTypingAnimation();
    }

    setupTypingAnimation() {
        const typingElement = document.querySelector('.typing-text');
        if (!typingElement) return;

        const text = this.currentLang === 'ar' ? 
            typingElement.getAttribute('data-ar') : 
            typingElement.getAttribute('data-en');

        // Reset animation
        typingElement.style.animation = 'none';
        typingElement.textContent = '';
        
        setTimeout(() => {
            typingElement.textContent = text;
            typingElement.style.animation = 'typing 3s steps(30) forwards, blink 1s infinite';
        }, 100);
    }

    setupScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Special handling for programming cards
                    if (entry.target.classList.contains('programming-card')) {
                        this.animateProgrammingCard(entry.target);
                    }
                    
                    // Special handling for timeline items
                    if (entry.target.classList.contains('timeline-item')) {
                        this.animateTimelineItem(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        const animateElements = document.querySelectorAll('.skill-card, .programming-card, .timeline-item, .contact-item');
        animateElements.forEach(el => observer.observe(el));
    }

    animateSkillDetails(card) {
        const details = card.querySelector('.skill-details');
        const items = details.querySelectorAll('li');
        
        items.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 100);
        });
    }

    resetSkillDetails(card) {
        const details = card.querySelector('.skill-details');
        const items = details.querySelectorAll('li');
        
        items.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
        });
    }

    animateProgrammingCard(card) {
        const levelFill = card.querySelector('.level-fill');
        if (levelFill) {
            const targetWidth = levelFill.style.width;
            levelFill.style.width = '0';
            setTimeout(() => {
                levelFill.style.width = targetWidth;
            }, 500);
        }
    }

    animateTimelineItem(item) {
        const content = item.querySelector('.timeline-content');
        content.style.opacity = '0';
        content.style.transform = 'translateY(50px)';

        setTimeout(() => {
            content.style.transition = 'all 0.6s ease';
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 200);
    }

    handleContactClick(e, card) {
        // Add click animation
        card.style.transform = 'scale(0.95)';
        setTimeout(() => {
            card.style.transform = 'translateY(-10px)';
        }, 150);

        // Add ripple effect
        this.createRippleEffect(e, card);

        // Show success message
        this.showContactMessage(card);
    }

    animateContactCard(card) {
        const icon = card.querySelector('i');
        icon.style.transform = 'scale(1.2) rotate(10deg)';

        setTimeout(() => {
            icon.style.transform = 'scale(1.2) rotate(-10deg)';
        }, 200);

        setTimeout(() => {
            icon.style.transform = 'scale(1.2) rotate(0deg)';
        }, 400);
    }

    createRippleEffect(e, element) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 10;
        `;

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    showContactMessage(card) {
        const contactValue = card.querySelector('.contact-value').textContent;
        const contactLabel = card.querySelector('.contact-label').textContent;
        const message = document.createElement('div');

        message.className = 'copy-notification';
        message.style.direction = this.currentLang === 'ar' ? 'rtl' : 'ltr';

        const icon = contactValue.includes('@') ? 'fas fa-envelope' :
                    contactValue.includes('+') ? 'fas fa-phone' :
                    contactValue.includes('wa.me') ? 'fab fa-whatsapp' :
                    'fas fa-copy';

        const messageText = this.currentLang === 'ar' ?
            `تم نسخ ${contactLabel}: ${contactValue}` :
            `${contactLabel} copied: ${contactValue}`;

        message.innerHTML = `
            <i class="${icon}"></i>
            <span>${messageText}</span>
        `;

        document.body.appendChild(message);

        // Copy to clipboard if it's email or phone
        if (contactValue.includes('@') || contactValue.includes('+')) {
            navigator.clipboard.writeText(contactValue).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = contactValue;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
            });
        }

        setTimeout(() => {
            message.classList.add('show');
        }, 100);

        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => {
                message.remove();
            }, 300);
        }, 3000);
    }

    async downloadPDF() {
        const pdfBtn = document.getElementById('pdf-download');
        const originalContent = pdfBtn.innerHTML;

        // Show loading state
        pdfBtn.classList.add('loading');
        pdfBtn.innerHTML = `
            <i class="fas fa-spinner"></i>
            <span data-ar="جاري التحميل..." data-en="Downloading...">
                ${this.currentLang === 'ar' ? 'جاري التحميل...' : 'Downloading...'}
            </span>
        `;

        try {
            // Hide elements that shouldn't appear in PDF
            this.preparePDFView();

            // Wait for fonts and images to load
            await this.waitForContent();

            // Create PDF
            const element = document.body;
            const canvas = await html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: this.isDarkMode ? '#1a1a1a' : '#ffffff',
                width: element.scrollWidth,
                height: element.scrollHeight,
                scrollX: 0,
                scrollY: 0
            });

            const imgData = canvas.toDataURL('image/png');
            const { jsPDF } = window.jspdf;

            // Calculate PDF dimensions
            const imgWidth = 210; // A4 width in mm
            const pageHeight = 295; // A4 height in mm
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            // Create PDF with proper orientation and language support
            const pdf = new jsPDF('p', 'mm', 'a4');

            // Add first page
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            // Add additional pages if needed
            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            // Generate filename based on language
            const fileName = this.currentLang === 'ar' ?
                'عبدالرؤوف_نبيل_عبدالرؤوف_CV.pdf' :
                'Abdel_Raouf_Nabil_CV.pdf';

            // Download PDF
            pdf.save(fileName);

            // Show success message
            this.showPDFSuccessMessage();

        } catch (error) {
            console.error('Error generating PDF:', error);
            this.showPDFErrorMessage();
        } finally {
            // Restore original state
            this.restorePDFView();
            pdfBtn.classList.remove('loading');
            pdfBtn.innerHTML = originalContent;
        }
    }

    preparePDFView() {
        // Hide navigation and loading screen for PDF
        const navbar = document.querySelector('.navbar');
        const loadingScreen = document.getElementById('loading-screen');
        const floatingElements = document.querySelector('.floating-elements');

        if (navbar) navbar.style.display = 'none';
        if (loadingScreen) loadingScreen.style.display = 'none';
        if (floatingElements) floatingElements.style.display = 'none';

        // Temporarily disable animations
        document.body.style.animation = 'none';
        const animatedElements = document.querySelectorAll('*');
        animatedElements.forEach(el => {
            el.style.animation = 'none';
            el.style.transition = 'none';
        });

        // Ensure all content is visible
        const skillDetails = document.querySelectorAll('.skill-details');
        skillDetails.forEach(detail => {
            detail.style.maxHeight = 'none';
            detail.style.opacity = '1';
        });

        // Expand all sections
        const sections = document.querySelectorAll('section');
        sections.forEach(section => {
            section.style.pageBreakInside = 'avoid';
        });
    }

    restorePDFView() {
        // Restore navigation
        const navbar = document.querySelector('.navbar');
        const floatingElements = document.querySelector('.floating-elements');

        if (navbar) navbar.style.display = '';
        if (floatingElements) floatingElements.style.display = '';

        // Restore animations
        document.body.style.animation = '';
        const animatedElements = document.querySelectorAll('*');
        animatedElements.forEach(el => {
            el.style.animation = '';
            el.style.transition = '';
        });

        // Restore skill details behavior
        const skillDetails = document.querySelectorAll('.skill-details');
        skillDetails.forEach(detail => {
            detail.style.maxHeight = '';
            detail.style.opacity = '';
        });
    }

    async waitForContent() {
        // Wait for images to load
        const images = document.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            if (img.complete) return Promise.resolve();
            return new Promise(resolve => {
                img.onload = resolve;
                img.onerror = resolve;
            });
        });

        await Promise.all(imagePromises);

        // Wait for fonts to load
        if (document.fonts) {
            await document.fonts.ready;
        }

        // Additional wait for any remaining content
        return new Promise(resolve => setTimeout(resolve, 1000));
    }

    showPDFSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'pdf-notification success';
        message.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${this.currentLang === 'ar' ? 'تم تحميل الـ PDF بنجاح!' : 'PDF downloaded successfully!'}</span>
        `;
        this.showNotification(message);
    }

    showPDFErrorMessage() {
        const message = document.createElement('div');
        message.className = 'pdf-notification error';
        message.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${this.currentLang === 'ar' ? 'حدث خطأ في تحميل الـ PDF' : 'Error downloading PDF'}</span>
        `;
        this.showNotification(message);
    }

    showNotification(notification) {
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            transform: translateX(100%);
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            direction: ${this.currentLang === 'ar' ? 'rtl' : 'ltr'};
        `;

        if (notification.classList.contains('success')) {
            notification.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            notification.style.color = 'white';
        } else {
            notification.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
            notification.style.color = 'white';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 4000);
    }

    // Matrix rain effect for tech demonstration
    createMatrixRain() {
        const canvas = document.createElement('canvas');
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '-1';
        canvas.style.opacity = '0.1';
        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
        const matrixArray = matrix.split("");

        const fontSize = 10;
        const columns = canvas.width / fontSize;

        const drops = [];
        for (let x = 0; x < columns; x++) {
            drops[x] = 1;
        }

        function draw() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#0F3';
            ctx.font = fontSize + 'px arial';

            for (let i = 0; i < drops.length; i++) {
                const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }
        }

        setInterval(draw, 35);
    }

    // Add floating particles effect
    createFloatingParticles() {
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'particles-container';
        particlesContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        `;
        document.body.appendChild(particlesContainer);

        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: var(--accent-primary);
                border-radius: 50%;
                opacity: 0.3;
                animation: float ${Math.random() * 3 + 2}s ease-in-out infinite;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation-delay: ${Math.random() * 2}s;
            `;
            particlesContainer.appendChild(particle);
        }
    }
}

// Initialize the CV Manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const cvManager = new CVManager();
    
    // Add some tech effects
    setTimeout(() => {
        cvManager.createFloatingParticles();
    }, 3000);
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideUp 0.6s ease forwards;
    }

    .particle {
        animation: particleFloat 4s ease-in-out infinite !important;
    }

    @keyframes particleFloat {
        0%, 100% {
            transform: translateY(0) rotate(0deg);
            opacity: 0.3;
        }
        50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 0.6;
        }
    }

    /* Copy notification styles */
    .copy-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #4CAF50, #45a049);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        z-index: 10000;
        transform: translateX(100%);
        transition: all 0.3s ease;
        font-family: 'Cairo', sans-serif;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .copy-notification.show {
        transform: translateX(0);
    }

    .copy-notification i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);
