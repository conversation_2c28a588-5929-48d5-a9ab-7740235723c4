<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My CV</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body class="dark-mode">
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="code-animation">
                <span>&lt;</span>
                <span>CV</span>
                <span>/&gt;</span>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-code"></i>
                <span data-ar="عبدالرؤوف نبيل" data-en="Abdel Raouf Nabil">عبدالرؤوف نبيل</span>
            </div>
            <div class="nav-controls">
                <button id="pdf-download" class="control-btn pdf-btn">
                    <i class="fas fa-download"></i>
                    <span data-ar="تحميل PDF" data-en="Download PDF">تحميل PDF</span>
                </button>
                <button id="theme-toggle" class="control-btn">
                    <i class="fas fa-moon"></i>
                </button>
                <button id="lang-toggle" class="control-btn">
                    <span>EN</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <div class="profile-image">
                    <div class="image-placeholder">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="tech-orbit">
                        <div class="orbit-item python"><i class="fab fa-python"></i></div>
                        <div class="orbit-item php"><i class="fab fa-php"></i></div>
                        <div class="orbit-item windows"><i class="fab fa-windows"></i></div>
                        <div class="orbit-item linux"><i class="fab fa-linux"></i></div>
                    </div>
                </div>
                <div class="hero-text">
                    <h1 class="name-title">
                        <span class="typing-text" data-ar="عبدالرؤوف نبيل عبدالرؤوف" data-en="Abdel Raouf Nabil Abdel Raouf">عبدالرؤوف نبيل عبدالرؤوف</span>
                    </h1>
                    <p class="subtitle" data-ar="مطور برمجيات وخبير صيانة أجهزة" data-en="Software Developer & Hardware Specialist">مطور برمجيات وخبير صيانة أجهزة</p>
                    <div class="hero-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>20/2/2002</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-graduation-cap"></i>
                            <span data-ar="طالب حقوق - جامعة الإسكندرية" data-en="Law Student - Alexandria University">طالب حقوق - جامعة الإسكندرية</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="floating-elements">
                <div class="float-item code-bracket">{</div>
                <div class="float-item code-bracket">}</div>
                <div class="float-item binary">01</div>
                <div class="float-item binary">10</div>
                <div class="float-item binary">00</div>
            </div>
        </section>

        <!-- Skills Section -->
        <section class="skills-section">
            <h2 class="section-title" data-ar="المهارات التقنية" data-en="Technical Skills">المهارات التقنية</h2>
            <div class="skills-grid">
                <!-- Hardware Skills -->
                <div class="skill-card" data-category="hardware">
                    <div class="skill-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3 data-ar="صيانة الأجهزة" data-en="Hardware Maintenance">صيانة الأجهزة</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="تشخيص أعطال الهاردوير" data-en="Hardware troubleshooting">تشخيص أعطال الهاردوير</li>
                            <li data-ar="استبدال المكونات" data-en="Component replacement">استبدال المكونات</li>
                        </ul>
                    </div>
                </div>

                <!-- Software Skills -->
                <div class="skill-card" data-category="software">
                    <div class="skill-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <h3 data-ar="صيانة البرمجيات" data-en="Software Maintenance">صيانة البرمجيات</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="إزالة الفيروسات والبرمجيات الخبيثة" data-en="Virus and malware removal">إزالة الفيروسات والبرمجيات الخبيثة</li>
                            <li data-ar="تحسين أداء النظام" data-en="System optimization">تحسين أداء النظام</li>
                            <li data-ar="استعادة البيانات" data-en="Data recovery">استعادة البيانات</li>
                            <li data-ar="تثبيت وتحديث البرامج" data-en="Software installation & updates">تثبيت وتحديث البرامج</li>
                        </ul>
                    </div>
                </div>

                <!-- Operating Systems -->
                <div class="skill-card" data-category="os">
                    <div class="skill-icon">
                        <i class="fab fa-windows"></i>
                    </div>
                    <h3 data-ar="أنظمة التشغيل" data-en="Operating Systems">أنظمة التشغيل</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="Windows (جميع الإصدارات)" data-en="Windows (All versions)">Windows (جميع الإصدارات)</li>
                            <li data-ar="Linux (Ubuntu, CentOS)" data-en="Linux (Ubuntu, CentOS)">Linux (Ubuntu, CentOS)</li>
                            <li data-ar="إدارة الخوادم" data-en="Server administration">إدارة الخوادم</li>
                            <li data-ar="أمان الشبكات" data-en="Network security">أمان الشبكات</li>
                        </ul>
                    </div>
                </div>

                <!-- Microsoft Office -->
                <div class="skill-card" data-category="office">
                    <div class="skill-icon">
                        <i class="fab fa-microsoft"></i>
                    </div>
                    <h3 data-ar="مايكروسوفت أوفيس" data-en="Microsoft Office">مايكروسوفت أوفيس</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="Excel المتقدم (الماكرو والدوال)" data-en="Advanced Excel (Macros & Functions)">Excel المتقدم (الماكرو والدوال)</li>
                            <li data-ar="Word (التنسيق المتقدم)" data-en="Word (Advanced formatting)">Word (التنسيق المتقدم)</li>
                            <li data-ar="PowerPoint (العروض التفاعلية)" data-en="PowerPoint (Interactive presentations)">PowerPoint (العروض التفاعلية)</li>
                            <li data-ar="Access (قواعد البيانات)" data-en="Access (Database management)">Access (قواعد البيانات)</li>
                        </ul>
                    </div>
                </div>

                <!-- Adobe Products -->
                <div class="skill-card" data-category="adobe">
                    <div class="skill-icon">
                        <i class="fab fa-adobe"></i>
                    </div>
                    <h3 data-ar="منتجات أدوبي" data-en="Adobe Products">منتجات أدوبي</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="Photoshop (تحرير الصور)" data-en="Photoshop (Photo editing)">Photoshop (تحرير الصور)</li>
                            <li data-ar="Illustrator (التصميم الجرافيكي)" data-en="Illustrator (Graphic design)">Illustrator (التصميم الجرافيكي)</li>
                            <li data-ar="Premiere Pro (مونتاج الفيديو)" data-en="Premiere Pro (Video editing)">Premiere Pro (مونتاج الفيديو)</li>
                            <li data-ar="After Effects (الموشن جرافيك)" data-en="After Effects (Motion graphics)">After Effects (الموشن جرافيك)</li>
                        </ul>
                    </div>
                </div>

                <!-- Accounting & POS -->
                <div class="skill-card" data-category="accounting">
                    <div class="skill-icon">
                        <i class="fas fa-cash-register"></i>
                    </div>
                    <h3 data-ar="برامج الحسابات و POS" data-en="Accounting & POS Systems">برامج الحسابات و POS</h3>
                    <div class="skill-details">
                        <ul>
                            <li data-ar="أنظمة نقاط البيع" data-en="Point of Sale systems">أنظمة نقاط البيع</li>
                            <li data-ar="إدارة المخزون" data-en="Inventory management">إدارة المخزون</li>
                            <li data-ar="التقارير المالية" data-en="Financial reporting">التقارير المالية</li>
                            <li data-ar="ربط الأنظمة المحاسبية" data-en="Accounting system integration">ربط الأنظمة المحاسبية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Programming Section -->
        <section class="programming-section">
            <h2 class="section-title" data-ar="مهارات البرمجة" data-en="Programming Skills">مهارات البرمجة</h2>
            <div class="programming-grid">
                <!-- Python -->
                <div class="programming-card python-card">
                    <div class="programming-header">
                        <i class="fab fa-python"></i>
                        <h3>Python</h3>
                        <div class="skill-level">
                            <span data-ar="خبير" data-en="Expert">خبير</span>
                            <div class="level-bar">
                                <div class="level-fill" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="programming-details">
                        <ul>
                            <li data-ar="تطوير تطبيقات سطح المكتب" data-en="Desktop application development">تطوير تطبيقات سطح المكتب</li>
                            <li data-ar="أتمتة المهام" data-en="Task automation">أتمتة المهام</li>
                            <li data-ar="تحليل البيانات" data-en="Data analysis">تحليل البيانات</li>
                            <li data-ar="الذكاء الاصطناعي" data-en="Artificial Intelligence">الذكاء الاصطناعي</li>
                            <li data-ar="تطوير الويب (Django/Flask)" data-en="Web development (Django/Flask)">تطوير الويب (Django/Flask)</li>
                        </ul>
                    </div>
                </div>

                <!-- PHP -->
                <div class="programming-card php-card">
                    <div class="programming-header">
                        <i class="fab fa-php"></i>
                        <h3>PHP</h3>
                        <div class="skill-level">
                            <span data-ar="متوسط" data-en="Intermediate">متوسط</span>
                            <div class="level-bar">
                                <div class="level-fill" style="width: 70%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="programming-details">
                        <ul>
                            <li data-ar="تطوير مواقع الويب" data-en="Web development">تطوير مواقع الويب</li>
                            <li data-ar="إدارة قواعد البيانات" data-en="Database management">إدارة قواعد البيانات</li>
                            <li data-ar="APIs وخدمات الويب" data-en="APIs and web services">APIs وخدمات الويب</li>
                            <li data-ar="أنظمة إدارة المحتوى" data-en="Content Management Systems">أنظمة إدارة المحتوى</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Experience Section -->
        <section class="experience-section">
            <h2 class="section-title" data-ar="الخبرة العملية" data-en="Work Experience">الخبرة العملية</h2>
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h3 data-ar="خبير صيانة أجهزة" data-en="Hardware Maintenance Specialist">خبير صيانة أجهزة</h3>
                        <span class="duration" data-ar="سنتان" data-en="2 Years">سنتان</span>
                        <p data-ar="تخصص في صيانة وإصلاح الأجهزة الإلكترونية والحاسوب" data-en="Specialized in maintenance and repair of electronic devices and computers">تخصص في صيانة وإصلاح الأجهزة الإلكترونية والحاسوب</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h3 data-ar="محاسب" data-en="Accountant">محاسب</h3>
                        <span class="duration" data-ar="سنة واحدة" data-en="1 Year">سنة واحدة</span>
                        <p data-ar="العمل في مجال المحاسبة وإدارة الأنظمة المالية" data-en="Working in accounting and financial systems management">العمل في مجال المحاسبة وإدارة الأنظمة المالية</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact-section">
            <h2 class="section-title" data-ar="تواصل معي" data-en="Contact Me">تواصل معي</h2>
            <div class="contact-grid">
                <div class="contact-item" onclick="window.open('mailto:<EMAIL>', '_blank')">
                    <i class="fas fa-envelope"></i>
                    <div class="contact-info">
                        <span class="contact-label" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</span>
                        <span class="contact-value"><EMAIL></span>
                    </div>
                </div>
                <div class="contact-item" onclick="window.open('tel:+************', '_blank')">
                    <i class="fas fa-phone"></i>
                    <div class="contact-info">
                        <span class="contact-label" data-ar="الهاتف" data-en="Phone">الهاتف</span>
                        <span class="contact-value">+************</span>
                    </div>
                </div>
                <div class="contact-item" onclick="window.open('https://wa.me/+************', '_blank')">
                    <i class="fab fa-whatsapp"></i>
                    <div class="contact-info">
                        <span class="contact-label" data-ar="واتساب" data-en="WhatsApp">واتساب</span>
                        <span class="contact-value">+************</span>
                    </div>
                </div>
                <div class="contact-item" onclick="window.open('https://link.gettap.co/1f0d63132b', '_blank')">
                    <i class="fas fa-id-card"></i>
                    <div class="contact-info">
                        <span class="contact-label" data-ar="بطاقة التعريف" data-en="Digital Card">بطاقة التعريف</span>
                        <span class="contact-value">Tap Card</span>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="script.js"></script>
</body>
</html>
